package com.mitdd.gazetracker.movement.follow

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.PointF
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.countdown
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.GazeTrajectory
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.movement.follow.bean.FollowAbilityEvaluateResult2

/**
 * FileName: FollowAbilityEvaluatingFragment2
 * Author by lilin,Date on 2025/5/8 16:31
 * PS: Not easy to write code, please indicate.
 * 九宫格路线追随能力检测Fragment
 */
class FollowAbilityEvaluatingFragment2 : BaseCommonFragment() {

    companion object {
        private val TAG = FollowAbilityEvaluatingFragment2::class.java.simpleName
        fun newInstance(): FollowAbilityEvaluatingFragment2 {
            return FollowAbilityEvaluatingFragment2()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_follow_ability_evaluating2
    }

    private val evaluatingView by id<FollowAbilityEvaluatingView2>(R.id.evaluating_view)
    private val clCountDown by id<ConstraintLayout>(R.id.cl_count_down)
    private val tvCountDown by id<TextView>(R.id.tv_count_down)

    private val mGson = Gson()

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    private var mGazeTrajectory:GazeTrajectory? = null

    override fun initView() {
        super.initView()

        startPromptCountdown()
    }

    /**
     * 开始评估
     */
    private fun startEvaluating(){
        clCountDown.isVisible = false
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_APPLIED_FOLLOW
            }
        )
    }

    /**
     * 停止评估
     */
    private fun stopEvaluating(){
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_STOP_APPLIED_FOLLOW
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_STOP_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_OFF_CAMERA
            }
        )
    }

    /**
     * 提示倒计时
     */
    private fun startPromptCountdown(){
        countdown(3000,1000,
            onTick = {
                tvCountDown.text = (it / 1000).toString()
            },
            onCompletion = { th ->
                Logger.d(TAG, msg = "startPromptCountdown onCompletion th = $th")
                if (th == null){
                    startEvaluating()
                }
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startPromptCountdown onCatch th = $th")
            }
        )
    }

    private fun parseMessage(msg: Message){
        when(msg.what){
            GazeConstants.MSG_APPLIED_FOLLOW_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_APPLIED_FOLLOW_STATE state = $state")
                if (state){
                    evaluatingView.startEvaluating {
                        sendMessageToService(Message.obtain().apply {
                            what = GazeConstants.MSG_GET_GAZE_TRAJECTORY
                        })
                    }
                }
            }
            GazeConstants.MSG_GAZE_TRAJECTORY_RESULT ->{
                val json = msg.data.getString(GazeConstants.KEY_GAZE_TRAJECTORY)
                Logger.d(TAG, msg = "MSG_GET_GAZE_TRAJECTORY_CALLBACK = $json")
                mGazeTrajectory = try {
                    mGson.fromJson(json, GazeTrajectory::class.java)
                }catch (e:Exception){
                    if (BuildConfig.DEBUG){
                        e.printStackTrace()
                    }
                    null
                }

                stopEvaluating()

                // 生成固定的追随路径点数据（基于FollowAbilityEvaluatingView2中的路径）
                val followPathPoints = generateFollowPathPoints()

                val followAbilityEvaluateResult = FollowAbilityEvaluateResult2(
                    gazePoints = mGazeTrajectory?.gaze?: emptyList(),
                    followPathPoints = followPathPoints
                )
                LiveEventBus.get<FollowAbilityEvaluateResult2>(FollowAbilityEvaluateResultActivity2.INPUT_PARAM_EVALUATE_RESULT).post(followAbilityEvaluateResult)
                startActivity(FollowAbilityEvaluateResultActivity2.createIntent(mActivity))
                mActivity.finish()
            }
        }
    }

    /**
     * 生成追随路径点数据（基于FollowAbilityEvaluatingView2中的固定路径）
     */
    private fun generateFollowPathPoints(): List<List<PointF>> {
        val screenWidth = resources.displayMetrics.widthPixels.toFloat()
        val screenHeight = resources.displayMetrics.heightPixels.toFloat()

        // 根据FollowAbilityEvaluatingView2中的路径生成路径点
        val pathPoints = mutableListOf<PointF>()

        // 添加路径上的关键点（基于FollowAbilityEvaluatingView2的路径定义）
        pathPoints.add(PointF(0.1f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.1f * screenWidth, 0.9f * screenHeight))
        pathPoints.add(PointF(0.2f * screenWidth, 0.9f * screenHeight))
        pathPoints.add(PointF(0.2f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.3f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.3f * screenWidth, 0.9f * screenHeight))
        pathPoints.add(PointF(0.4f * screenWidth, 0.9f * screenHeight))
        pathPoints.add(PointF(0.4f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.5f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.5f * screenWidth, 0.9f * screenHeight))
        pathPoints.add(PointF(0.6f * screenWidth, 0.9f * screenHeight))
        pathPoints.add(PointF(0.6f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.7f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.7f * screenWidth, 0.9f * screenHeight))
        pathPoints.add(PointF(0.8f * screenWidth, 0.9f * screenHeight))
        pathPoints.add(PointF(0.8f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.9f * screenWidth, 0.1f * screenHeight))
        pathPoints.add(PointF(0.9f * screenWidth, 0.9f * screenHeight))

        // 将路径点转换为屏幕比例坐标
        val normalizedPathPoints = pathPoints.map { point ->
            PointF(point.x / screenWidth, point.y / screenHeight)
        }

        // 返回单个路径段
        return listOf(normalizedPathPoints)
    }

    override fun onStart() {
        super.onStart()
        mActivity.bindService(Intent(mActivity, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onStop() {
        super.onStop()
        stopEvaluating()
        mActivity.unbindService(serviceConnection)
    }

    private fun sendMessageToService(vararg messages: Message){
        messages.forEach {
            mServiceMessage?.send(it)
        }
    }

}