package com.mitdd.gazetracker.movement.follow.bean

import android.graphics.PointF
import android.os.Parcelable
import com.mitdd.gazetracker.gaze.bean.GazePoint
import kotlinx.android.parcel.Parcelize

/**
 * FileName: FollowAbilityEvaluateResult2
 * Author by lilin,Date on 2025/5/8 16:44
 * PS: Not easy to write code, please indicate.
 */
@Parcelize
data class FollowAbilityEvaluateResult2(
    var gazePoints:List<GazePoint>,
    var followPathPoints: List<List<PointF>> = emptyList()
): Parcelable
