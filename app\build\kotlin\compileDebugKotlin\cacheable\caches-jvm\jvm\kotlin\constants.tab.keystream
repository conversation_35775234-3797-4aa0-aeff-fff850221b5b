#com/mitdd/gazetracker/GTApplicationcom/mitdd/gazetracker/ServiceId:com/mitdd/gazetracker/common/dialog/task/DialogTaskManager,com/mitdd/gazetracker/device/DeviceConstants.com/mitdd/gazetracker/flipbeat/FlipBeatManager(com/mitdd/gazetracker/gaze/GazeConstants(com/mitdd/gazetracker/gaze/GazeErrorCode.com/mitdd/gazetracker/gaze/GazeTrackingManager+com/mitdd/gazetracker/gaze/bean/GazeMessage8com/mitdd/gazetracker/gaze/widget/PostureCalibrationView+com/mitdd/gazetracker/help/TutorialActivity4com/mitdd/gazetracker/medicalhome/bean/TreatmentInfo:com/mitdd/gazetracker/medicalhome/mask/MaskTherapyFragment>com/mitdd/gazetracker/medicalhome/mask/SelectCommonAppActivity>com/mitdd/gazetracker/medicalhome/preview/ParamSettingActivityBcom/mitdd/gazetracker/medicalhome/receiver/RefreshBindUserReceiver;com/mitdd/gazetracker/medicalhome/train/SelectTrainActivityAcom/mitdd/gazetracker/medicalhome/train/TrainCategoryListFragment9com/mitdd/gazetracker/medicalhome/train/TrainListFragment8com/mitdd/gazetracker/medicalhome/train/TrainWebActivity;com/mitdd/gazetracker/medicalhome/train/VisualTrainFragment;com/mitdd/gazetracker/medicalhospital/bean/PatientTrainDataIcom/mitdd/gazetracker/medicalhospital/inspection/InspectionCenterActivity>com/mitdd/gazetracker/medicalhospital/mt/MHMTTrainDataFragment<com/mitdd/gazetracker/medicalhospital/mt/MHospitalMTActivity<com/mitdd/gazetracker/medicalhospital/mt/MHospitalMTFragment?com/mitdd/gazetracker/medicalhospital/mt/PatientLibraryFragmentIcom/mitdd/gazetracker/movement/follow/FollowAbilityEvaluateResultActivityJcom/mitdd/gazetracker/movement/follow/FollowAbilityEvaluateResultActivity2Gcom/mitdd/gazetracker/movement/gaze/GazeStabilityEvaluateResultActivity<com/mitdd/gazetracker/movement/patient/EMPatientInfoFragment?com/mitdd/gazetracker/movement/patient/EMPatientLibraryFragment7com/mitdd/gazetracker/movement/roi/ROIDetectionActivity=com/mitdd/gazetracker/movement/roi/ROIDetectionResultActivityKcom/mitdd/gazetracker/movement/saccade/SaccadeAbilityEvaluateResultActivity(com/mitdd/gazetracker/mqtt/MQTTConstants#com/mitdd/gazetracker/net/UrlConfig5com/mitdd/gazetracker/read/ReadResultAnalysisActivity,com/mitdd/gazetracker/read/ReadTrackActivity1com/mitdd/gazetracker/read/home/<USER>/ReadHomeMode<com/mitdd/gazetracker/read/home/<USER>/MyopiaControlFragmentEcom/mitdd/gazetracker/read/home/<USER>/MyopiaTrainCategoryListFragment9com/mitdd/gazetracker/read/home/<USER>/MyopiaTrainFragment=com/mitdd/gazetracker/read/home/<USER>/MyopiaTrainListFragment+com/mitdd/gazetracker/update/UpdateActivity.com/mitdd/gazetracker/user/ProtocolWebActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  