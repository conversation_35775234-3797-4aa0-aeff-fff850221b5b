package com.mitdd.gazetracker.movement.follow

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.widget.Toast
import androidx.core.view.drawToBitmap
import androidx.lifecycle.ViewModelProvider
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.EyeMovementResultActivity
import com.mitdd.gazetracker.movement.follow.bean.FollowAbilityEvaluateResult2
import com.mitdd.gazetracker.movement.patient.EMPatientManager
import com.mitdd.gazetracker.movement.vm.FollowAbilityViewModel
import java.io.File
import java.io.FileOutputStream

/**
 * FileName: FollowAbilityEvaluateResultActivity2
 * Author by lilin,Date on 2025/5/8 16:56
 * PS: Not easy to write code, please indicate.
 */
class FollowAbilityEvaluateResultActivity2 : EyeMovementResultActivity() {

    companion object{
        private val TAG = FollowAbilityEvaluateResultActivity2::class.java.simpleName

        const val INPUT_PARAM_EVALUATE_RESULT = "evaluate_result"

        fun createIntent(context: Context): Intent {
            return Intent(context, FollowAbilityEvaluateResultActivity2::class.java)
        }
    }

    private val evaluateResultView by id<FollowAbilityEvaluateResultView2>(R.id.evaluate_result_view)

    // ViewModel
    private lateinit var followAbilityViewModel: FollowAbilityViewModel

    // 数据
    private var gazePoints: List<GazePoint> = emptyList()
    private var followPathPoints: List<List<android.graphics.PointF>> = emptyList()
    private var isImageUploaded = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_follow_ability_evaluate_result2)

        // 初始化ViewModel
        followAbilityViewModel = ViewModelProvider(this)[FollowAbilityViewModel::class.java]

        initView()
        initObserver()
        initData()
    }

    private fun initView(){
        getTitleView().text = getString(R.string.str_follow_ability_evaluate_result)
        getPointNumberView().isVisible = false
        getAverageDurationView().isVisible = false
    }

    private fun initObserver(){
        // 观察评估结果数据
        LiveEventBus.get<FollowAbilityEvaluateResult2>(INPUT_PARAM_EVALUATE_RESULT).observeSticky(this){
            if (it != null){
                gazePoints = it.gazePoints
                followPathPoints = it.followPathPoints
                evaluateResultView.drawResult(it)

                Logger.d(TAG, msg = "接收到追随能力评估结果数据")
                Logger.d(TAG, msg = "视线轨迹点数量: ${gazePoints.size}")
                Logger.d(TAG, msg = "追随路径段数量: ${followPathPoints.size}")

                // 等待视图绘制完成后获取截图并上传
                evaluateResultView.post {
                    captureAndUploadResultImage()
                }
            }
        }

        // 观察图片上传结果
        followAbilityViewModel.uploadImageResultLiveData.observe(this) { result ->
            if (result != null) {
                Logger.d(TAG, msg = "图片上传成功，URL: ${result.data?.url}")
                isImageUploaded = true
                // 图片上传成功后，提交数据到服务器
                submitDataToServerWithImage(result.data?.url)
            } else {
                Logger.e(TAG, msg = "图片上传失败")
                // 图片上传失败，仍然提交数据但不包含图片URL
                submitDataToServerWithImage(null)
            }
        }

        // 观察提交结果
        followAbilityViewModel.submitResultLiveData.observe(this) { result ->
            Logger.d(TAG, msg = "收到提交结果回调")
            if (result != null) {
                Logger.d(TAG, msg = "追随能力检测结果提交成功 - 记录ID: ${result.data}")
                Toast.makeText(this, "数据提交成功", Toast.LENGTH_SHORT).show()
            } else {
                Logger.e(TAG, msg = "追随能力检测结果提交失败")
                Toast.makeText(this, "数据提交失败", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun initData() {
        Logger.d(TAG, msg = "初始化追随能力评估结果页面数据")
    }

    override fun save() {
        // 保存图片到相册
        super.save()

        // 如果还没有上传图片，则重新上传
        if (!isImageUploaded) {
            captureAndUploadResultImage()
        }
    }

    /**
     * 获取结果页面截图并上传
     */
    private fun captureAndUploadResultImage() {
        try {
            Logger.d(TAG, msg = "开始获取追随能力评估结果截图")

            // 获取结果容器的截图
            val bitmap = resultContainer.drawToBitmap()
            val imageFile = saveBitmapToFile(bitmap)

            Logger.d(TAG, msg = "结果截图保存成功: ${imageFile.absolutePath}")
            Logger.d(TAG, msg = "图片大小: ${imageFile.length()} bytes")

            // 上传图片
            followAbilityViewModel.uploadImage(imageFile)
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取结果截图失败: ${e.message}")
            // 如果截图失败，直接提交数据不包含图片
            submitDataToServerWithImage(null)
        }
    }

    /**
     * 保存Bitmap到文件
     */
    private fun saveBitmapToFile(bitmap: Bitmap): File {
        val fileName = "follow_ability_result_${System.currentTimeMillis()}.png"
        val file = File(cacheDir, fileName)

        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
        }

        return file
    }

    /**
     * 提交数据到服务器（包含图片URL）
     */
    private fun submitDataToServerWithImage(imageUrl: String?) {
        val currentPatient = EMPatientManager.getEMPatient()
        if (currentPatient?.id == null) {
            Logger.e(TAG, msg = "当前患者信息为空，无法提交数据")
            Toast.makeText(this, "患者信息缺失，无法提交数据", Toast.LENGTH_SHORT).show()
            return
        }

        val patientId = try {
            currentPatient.id!!.toLong()
        } catch (e: NumberFormatException) {
            Toast.makeText(this, "患者ID格式错误", Toast.LENGTH_SHORT).show()
            return
        }

        if (gazePoints.isEmpty()) {
            Logger.e(TAG, msg = "视线轨迹数据为空，无法提交")
            Toast.makeText(this, "测试数据为空，无法提交", Toast.LENGTH_SHORT).show()
            return
        }

        Logger.d(TAG, msg = "开始提交追随能力检测数据")
        Logger.d(TAG, msg = "患者ID: $patientId, 轨迹点数量: ${gazePoints.size}, 路径段数量: ${followPathPoints.size}")
        Logger.d(TAG, msg = "图片URL: $imageUrl")

        followAbilityViewModel.submitFollowAbilityResult(
            patientId = patientId,
            followPathPoints = followPathPoints,
            gazePoints = gazePoints,
            duration = 60000,
            notes = "追随能力测试",
            imageUrl = imageUrl
        )
    }

    override fun getSerialNumberType():String {
        return "02"
    }

}