package com.mitdd.gazetracker.movement.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.movement.api.FollowAbilityApiService
import com.mitdd.gazetracker.movement.bean.FollowAbilityAdd
import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.net.UrlConfig
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: FollowAbilityRepository
 * Author by AI Assistant, Date on 2025/6/23
 * PS: Not easy to write code, please indicate.
 * 追随能力检测Repository
 */
class FollowAbilityRepository : BaseRepository() {

    companion object {
        private val TAG = FollowAbilityRepository::class.java.simpleName
    }

    /**
     * 上传图片
     * @param file 图片文件
     */
    suspend fun uploadImage(file: MultipartBody.Part): ApiResponse<FileUploadResponse> {
        Logger.d(TAG, msg = "开始上传追随能力检测结果图片")
        return executeHttp {
            val response = MainRetrofitClient.createService(FollowAbilityApiService::class.java, UrlConfig.MOVEMENT_DOMAIN)
                .uploadImage(file)
            Logger.d(TAG, msg = "追随能力检测结果图片上传完成")
            response
        }
    }

    /**
     * 提交追随能力检测结果
     * @param params 提交参数
     */
    suspend fun submitFollowAbilityResult(params: HashMap<String, Any>): ApiResponse<FollowAbilityAdd> {
        Logger.d(TAG, msg = "开始提交追随能力检测结果")
        Logger.d(TAG, msg = "提交参数: ${gson.toJson(params)}")
        
        return executeHttp {
            val json = gson.toJson(params)
            val requestBody = json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            
            Logger.d(TAG, msg = "发送追随能力检测结果HTTP请求")
            val response = MainRetrofitClient.createService(FollowAbilityApiService::class.java, UrlConfig.MOVEMENT_DOMAIN)
                .addFollowAbility(requestBody)
            Logger.d(TAG, msg = "追随能力检测结果HTTP请求完成")
            response
        }
    }
}
