kotlin.Annotation3com.airdoc.component.common.base.BaseCommonActivityandroid.app.Service!androidx.lifecycle.LifecycleOwner8com.mitdd.gazetracker.gaze.listener.IGazeAppliedListener6com.mitdd.gazetracker.gaze.listener.IGazeTrackListener1androidx.constraintlayout.widget.ConstraintLayout3com.mitdd.gazetracker.common.dialog.task.DialogTask!android.content.BroadcastReceiver8androidx.recyclerview.widget.RecyclerView.ItemDecoration4com.airdoc.component.common.base.BaseBindingFragment0com.airdoc.component.common.ui.web.CustomWebView8com.mitdd.gazetracker.movement.EyeMovementResultActivityandroid.widget.FrameLayout7com.airdoc.component.common.net.base.BaseRetrofitClientandroid.view.Viewandroid.widget.PopupWindow1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder3com.airdoc.component.common.base.BaseCommonFragment1com.airdoc.component.common.base.BaseCommonDialog)com.mitdd.gazetracker.base.GTBaseActivity,com.airdoc.component.common.cache.INameSpacekotlin.Enumandroid.os.Parcelable3com.airdoc.component.common.net.base.BaseRepositoryandroidx.lifecycle.ViewModel)org.java_websocket.server.WebSocketServer androidx.viewbinding.ViewBinding6com.airdoc.component.common.base.BaseCommonApplication!android.view.View.OnClickListener#android.view.animation.Interpolatorandroid.animation.TypeEvaluatorandroidx.work.Workerkotlin.Comparable+androidx.lifecycle.DefaultLifecycleObserver;com.mitdd.gazetracker.common.dialog.task.DialogTaskCallback+androidx.appcompat.widget.AppCompatEditText)org.java_websocket.client.WebSocketClient!kotlinx.coroutines.CoroutineScope1com.mitdd.gazetracker.gaze.camera.ICameraListener,androidx.appcompat.widget.AppCompatImageViewHcom.mitdd.gazetracker.medicalhome.train.TrainWebView.TrainActionListeneracom.mitdd.gazetracker.medicalhospital.inspection.InspectionCenterWebView.InspectionActionListenerXcom.mitdd.gazetracker.medicalhospital.train.TrainCenterWebView.TrainCenterActionListenerDcom.airdoc.component.common.ui.web.CustomWebView.CustomWebViewClient'com.luck.picture.lib.engine.ImageEngineAcom.aliyun.alink.linksdk.cmp.core.listener.IConnectNotifyListenerokhttp3.Interceptor                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        